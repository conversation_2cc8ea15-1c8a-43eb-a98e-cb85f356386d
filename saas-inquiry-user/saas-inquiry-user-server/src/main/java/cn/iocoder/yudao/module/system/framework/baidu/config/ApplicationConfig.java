package cn.iocoder.yudao.module.system.framework.baidu.config;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 百度应用配置类
 *
 * @Author:chenxia<PERSON>i
 * @Date:2025/08/06 15:19
 */
// @Configuration
public class ApplicationConfig {

    // @Value("${baidu.api.face.app-id}")
    // private String faceAppId;
    //
    // @Value("${baidu.api.face.app-key}")
    // private String faceAppKey;
    //
    // @Value("${baidu.api.face.secret-key}")
    // private String faceSecretKey;
    //
    // /**
    //  * 初始化一个AipFace
    //  */
    // @Bean
    // public AipFace aipFace() {
    //     AipFace client = new AipFace(faceAppId, faceAppKey, faceSecretKey);
    //     // 设置网络连接参数
    //     client.setConnectionTimeoutInMillis(2000);
    //     client.setSocketTimeoutInMillis(60000);
    //     return client;
    // }


}
